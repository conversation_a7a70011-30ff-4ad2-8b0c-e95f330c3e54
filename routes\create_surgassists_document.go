package routes

import (
	"log"

	"github.com/applegold/surgassists-ar-backend/utils"
	"github.com/gofiber/fiber/v2"
)

// CreateSurgassistsDocumentRequest represents the incoming request
type CreateSurgassistsDocumentRequest struct {
	PatientName  string `json:"patient_name"`
	PatientEmail string `json:"patient_email"`
	Title        string `json:"title,omitempty"`
}

// CreateSurgassistsDocument creates a document using your specific template
func CreateSurgassistsDocument(c *fiber.Ctx) error {
	log.Printf("=== CREATE SURGASSISTS DOCUMENT ===")

	// Parse request body
	var request CreateSurgassistsDocumentRequest
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate required fields
	if request.PatientName == "" || request.PatientEmail == "" {
		log.Printf("Missing required fields - PatientName: %s, PatientEmail: %s", request.PatientName, request.PatientEmail)
		return c.Status(400).JSON(fiber.Map{
			"error": "Patient name and email are required",
		})
	}

	// Set default title if not provided
	title := request.Title
	if title == "" {
		title = "Surgassists Document"
	}

	log.Printf("Creating document for patient: %s (%s) with title: %s", request.PatientName, request.PatientEmail, title)

	// Create Documenso client
	client := utils.NewDocumensoClient()

	// Create document using convenience function
	response, err := client.CreateSurgassistsDocument(request.PatientName, request.PatientEmail, title)
	if err != nil {
		log.Printf("Failed to create Surgassists document: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error":   "Failed to create document",
			"details": err.Error(),
		})
	}

	log.Printf("Surgassists document created successfully with ID: %d", response.DocumentID)

	return c.JSON(fiber.Map{
		"message":       "Surgassists document created successfully",
		"document_id":   response.DocumentID,
		"title":         response.Title,
		"status":        response.Status,
		"patient_name":  request.PatientName,
		"patient_email": request.PatientEmail,
		"response":      response,
	})
}
