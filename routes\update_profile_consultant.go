package routes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"

	"github.com/gofiber/fiber/v2"
)

// ConsultantProfileRequest represents the incoming request for updating consultant profile
type ConsultantProfileRequest struct {
	UserID         string `json:"UserID"`
	ConsultantName string `json:"ConsultantName"`
	Address1       string `json:"Address1"`
	Address2       string `json:"Address2"`
	PostCode       string `json:"PostCode"`
	Telephone      string `json:"Telephone"`
	Email          string `json:"Email"`
	Phone          string `json:"Phone"`
	Department     string `json:"Department"`
	Available      string `json:"Available"`
}

func UpdateProfileConsultant(c *fiber.Ctx) error {
	log.Printf("=== UPDATE PROFILE CONSULTANT ===")

	// Parse request body
	var request ConsultantProfileRequest
	if err := c.BodyParser(&request); err != nil {
		log.Printf("Failed to parse request body: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Invalid request body",
		})
	}

	// Validate required fields
	if request.UserID == "" {
		log.Printf("Missing required field: UserID")
		return c.Status(400).JSON(fiber.Map{
			"error": "UserID is required",
		})
	}

	log.Printf("Updating profile for UserID: %s", request.UserID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing")
		return c.Status(500).JSON(fiber.Map{
			"error": "Server configuration error",
		})
	}

	// Profile collection ID
	profileCollection := "6852be26001922ea0c85"

	// Step 1: Search for existing profile by UserID
	existingDocumentID, err := findProfileByUserID(request.UserID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection)
	if err != nil {
		log.Printf("Error searching for existing profile: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to search for existing profile",
		})
	}

	// Step 2: Update existing or create new profile
	if existingDocumentID != "" {
		// Update existing profile
		log.Printf("Found existing profile with document ID: %s, updating...", existingDocumentID)
		if err := updateExistingProfile(existingDocumentID, request, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection); err != nil {
			log.Printf("Failed to update existing profile: %v", err)
			return c.Status(500).JSON(fiber.Map{
				"error": "Failed to update profile",
			})
		}

		log.Printf("Profile updated successfully for UserID: %s", request.UserID)
		return c.JSON(fiber.Map{
			"message":     "Profile updated successfully",
			"user_id":     request.UserID,
			"document_id": existingDocumentID,
			"action":      "updated",
		})
	} else {
		// Create new profile
		log.Printf("No existing profile found, creating new profile...")
		newDocumentID, err := createNewProfile(request, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection)
		if err != nil {
			log.Printf("Failed to create new profile: %v", err)
			return c.Status(500).JSON(fiber.Map{
				"error": "Failed to create profile",
			})
		}

		log.Printf("Profile created successfully for UserID: %s with document ID: %s", request.UserID, newDocumentID)
		return c.JSON(fiber.Map{
			"message":     "Profile created successfully",
			"user_id":     request.UserID,
			"document_id": newDocumentID,
			"action":      "created",
		})
	}
}

// findProfileByUserID searches for an existing profile by UserID and returns the document ID if found
func findProfileByUserID(userID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) (string, error) {
	// Build the URL to search Profile collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	log.Printf("Searching for profile with UserID: %s", userID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create search request: %v", err)
	}

	// Set headers
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to search profiles: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read search response: %v", err)
	}

	log.Printf("Profile search response status: %d", resp.StatusCode)
	log.Printf("Profile search response: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("profile search failed with status: %d", resp.StatusCode)
	}

	// Parse response
	var searchResponse map[string]interface{}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return "", fmt.Errorf("failed to parse search response: %v", err)
	}

	// Look for documents with matching UserID
	if documents, exists := searchResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			for _, doc := range documentsArray {
				if docMap, ok := doc.(map[string]interface{}); ok {
					if userIDField, exists := docMap["UserID"]; exists {
						if userIDStr, ok := userIDField.(string); ok && userIDStr == userID {
							if docID, exists := docMap["$id"]; exists {
								if docIDStr, ok := docID.(string); ok {
									log.Printf("Found existing profile with document ID: %s", docIDStr)
									return docIDStr, nil
								}
							}
						}
					}
				}
			}
		}
	}

	log.Printf("No existing profile found for UserID: %s", userID)
	return "", nil
}

// updateExistingProfile updates an existing profile document
func updateExistingProfile(documentID string, request ConsultantProfileRequest, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) error {
	// Build the URL for updating the document
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, profileCollection, documentID)

	// Create the update payload
	updateData := map[string]interface{}{
		"data": map[string]interface{}{
			"UserID":         request.UserID,
			"ConsultantName": request.ConsultantName,
			"Address1":       request.Address1,
			"Address2":       request.Address2,
			"PostCode":       request.PostCode,
			"Telephone":      request.Telephone,
			"Email":          request.Email,
			"Phone":          request.Phone,
			"Department":     request.Department,
			"Available":      request.Available,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("failed to marshal update data: %v", err)
	}

	log.Printf("Updating profile document ID: %s", documentID)
	log.Printf("Update payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("PATCH", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create update request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update profile: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read update response: %v", err)
	}

	log.Printf("Profile update response status: %d", resp.StatusCode)
	log.Printf("Profile update response: %s", string(body))

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("profile update failed with status: %d, response: %s", resp.StatusCode, string(body))
	}

	return nil
}

// createNewProfile creates a new profile document
func createNewProfile(request ConsultantProfileRequest, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) (string, error) {
	// Build the URL for creating a new document
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	// Create the document payload
	documentData := map[string]interface{}{
		"documentId": "unique()", // Let Appwrite generate unique ID
		"data": map[string]interface{}{
			"UserID":         request.UserID,
			"ConsultantName": request.ConsultantName,
			"Address1":       request.Address1,
			"Address2":       request.Address2,
			"PostCode":       request.PostCode,
			"Telephone":      request.Telephone,
			"Email":          request.Email,
			"Phone":          request.Phone,
			"Department":     request.Department,
			"Available":      request.Available,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(documentData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal document data: %v", err)
	}

	log.Printf("Creating new profile document")
	log.Printf("Create payload: %s", string(jsonData))

	// Create HTTP request
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to create profile: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read create response: %v", err)
	}

	log.Printf("Profile create response status: %d", resp.StatusCode)
	log.Printf("Profile create response: %s", string(body))

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("profile creation failed with status: %d, response: %s", resp.StatusCode, string(body))
	}

	// Parse response to get the document ID
	var createResponse map[string]interface{}
	if err := json.Unmarshal(body, &createResponse); err != nil {
		return "", fmt.Errorf("failed to parse create response: %v", err)
	}

	// Extract document ID
	if docID, exists := createResponse["$id"]; exists {
		if docIDStr, ok := docID.(string); ok {
			return docIDStr, nil
		}
	}

	return "", fmt.Errorf("failed to extract document ID from create response")
}

// GetProfileConsultant retrieves a consultant profile by UserID
func GetProfileConsultant(c *fiber.Ctx) error {
	// Get the user ID from the URL parameter
	userID := c.Params("userid")

	if userID == "" {
		log.Printf("Missing user ID in URL for GetProfileConsultant")
		return c.Status(400).JSON(fiber.Map{
			"error": "User ID is required",
		})
	}

	log.Printf("=== GET PROFILE CONSULTANT ===")
	log.Printf("Fetching profile for UserID: %s", userID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing")
		return c.Status(500).JSON(fiber.Map{
			"error": "Server configuration error",
		})
	}

	// Profile collection ID
	profileCollection := "6852be26001922ea0c85"

	// Search for the profile by UserID
	profile, err := getProfileByUserID(userID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection)
	if err != nil {
		log.Printf("Error fetching profile: %v", err)
		// Check if it's a "not found" error
		if err.Error() == fmt.Sprintf("profile not found for UserID: %s", userID) {
			return c.Status(404).JSON(fiber.Map{
				"error":   "Profile not found",
				"user_id": userID,
			})
		}
		// Other errors are server errors
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to fetch profile",
		})
	}

	log.Printf("Profile fetched successfully for UserID: %s", userID)
	return c.JSON(fiber.Map{
		"message": "Profile fetched successfully",
		"user_id": userID,
		"profile": profile,
	})
}

// getProfileByUserID searches for a profile by UserID and returns the profile data
func getProfileByUserID(userID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) (map[string]interface{}, error) {
	// Build the URL to search Profile collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	log.Printf("Searching for profile with UserID: %s", userID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create search request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to search profiles: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read search response: %v", err)
	}

	log.Printf("Profile search response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("profile search failed with status: %d", resp.StatusCode)
	}

	// Parse response
	var searchResponse map[string]interface{}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return nil, fmt.Errorf("failed to parse search response: %v", err)
	}

	// Look for documents with matching UserID
	if documents, exists := searchResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			for _, doc := range documentsArray {
				if docMap, ok := doc.(map[string]interface{}); ok {
					if userIDField, exists := docMap["UserID"]; exists {
						if userIDStr, ok := userIDField.(string); ok && userIDStr == userID {
							log.Printf("Found matching profile for UserID: %s", userID)
							return docMap, nil
						}
					}
				}
			}
		}
	}

	log.Printf("No profile found for UserID: %s", userID)
	return nil, fmt.Errorf("profile not found for UserID: %s", userID)
}
