package routes

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"os"
	"strings"

	"github.com/gofiber/fiber/v2"
)

func UpdateProfileImageConsultant(c *fiber.Ctx) error {
	log.Printf("=== UPDATE PROFILE IMAGE CONSULTANT ===")

	// Get the user ID from the URL parameter
	userID := c.Params("id")
	if userID == "" {
		log.Printf("Missing user ID in URL for profile image update")
		return c.Status(400).JSON(fiber.Map{
			"error": "User ID is required",
		})
	}

	log.Printf("Updating profile image for UserID: %s", userID)

	// Get Appwrite configuration
	appwriteEndpoint := os.Getenv("APPWRITE_ENDPOINT")
	appwriteProjectID := os.Getenv("APPWRITE_PROJECT_ID")
	appwriteKey := os.Getenv("APPWRITE_KEY")
	appwriteDatabaseID := os.Getenv("APPWRITE_DATABASE_ID")

	if appwriteEndpoint == "" || appwriteProjectID == "" || appwriteKey == "" || appwriteDatabaseID == "" {
		log.Printf("Appwrite configuration missing for profile image update")
		return c.Status(500).JSON(fiber.Map{
			"error": "Server configuration error",
		})
	}

	// Get the uploaded image file
	file, err := c.FormFile("image")
	if err != nil {
		log.Printf("Failed to get uploaded image file: %v", err)
		return c.Status(400).JSON(fiber.Map{
			"error": "Image file is required",
		})
	}

	// Validate file type (basic image validation)
	if !isValidImageFile(file.Filename) {
		log.Printf("Invalid image file type: %s", file.Filename)
		return c.Status(400).JSON(fiber.Map{
			"error": "Only image files (jpg, jpeg, png, gif, webp) are allowed",
		})
	}

	log.Printf("Uploading image file: %s, Size: %d bytes", file.Filename, file.Size)

	// Step 1: Upload image to Appwrite Storage (ProfileImages bucket)
	profileImagesBucket := "6894d1760018d48dea36"
	fileID, imageURL, err := uploadImageToStorage(file, appwriteEndpoint, appwriteProjectID, appwriteKey, profileImagesBucket)
	if err != nil {
		log.Printf("Failed to upload image to storage: %v", err)
		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to upload image to storage",
		})
	}

	log.Printf("Image uploaded successfully with ID: %s", fileID)
	log.Printf("Image URL: %s", imageURL)

	// Step 2: Update Profile collection with the image URL
	profileCollection := "6852be26001922ea0c85"
	err = updateProfileImageURL(userID, imageURL, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection)
	if err != nil {
		log.Printf("Failed to update profile with image URL: %v", err)

		// Rollback: Delete the uploaded image from storage
		log.Printf("Rolling back: Deleting uploaded image %s from storage", fileID)
		deleteImageFromStorage(fileID, appwriteEndpoint, appwriteProjectID, appwriteKey, profileImagesBucket)

		return c.Status(500).JSON(fiber.Map{
			"error": "Failed to update profile with image",
		})
	}

	log.Printf("Profile image updated successfully for UserID: %s", userID)
	return c.JSON(fiber.Map{
		"message":   "Profile image updated successfully",
		"user_id":   userID,
		"file_id":   fileID,
		"image_url": imageURL,
		"filename":  file.Filename,
		"size":      file.Size,
	})
}

// isValidImageFile checks if the file has a valid image extension
func isValidImageFile(filename string) bool {
	validExtensions := []string{".jpg", ".jpeg", ".png", ".gif", ".webp", ".bmp"}
	filename = strings.ToLower(filename)

	for _, ext := range validExtensions {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}

// uploadImageToStorage uploads an image file to Appwrite Storage and returns the file ID and URL
func uploadImageToStorage(file *multipart.FileHeader, appwriteEndpoint, appwriteProjectID, appwriteKey, bucketID string) (string, string, error) {
	// Build storage upload URL
	storageURL := fmt.Sprintf("%s/storage/buckets/%s/files", appwriteEndpoint, bucketID)

	// Open the uploaded file
	fileContent, err := file.Open()
	if err != nil {
		return "", "", fmt.Errorf("failed to open uploaded file: %v", err)
	}
	defer fileContent.Close()

	// Create multipart form for Appwrite storage upload
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// Add fileId field (let Appwrite generate unique ID)
	writer.WriteField("fileId", "unique()")

	// Add file field
	part, err := writer.CreateFormFile("file", file.Filename)
	if err != nil {
		return "", "", fmt.Errorf("failed to create form file: %v", err)
	}

	// Copy file content to form
	_, err = io.Copy(part, fileContent)
	if err != nil {
		return "", "", fmt.Errorf("failed to copy file content: %v", err)
	}

	writer.Close()

	// Create HTTP request for storage upload
	req, err := http.NewRequest("POST", storageURL, &requestBody)
	if err != nil {
		return "", "", fmt.Errorf("failed to create storage upload request: %v", err)
	}

	// Set headers for Appwrite Storage API
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the storage upload request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", "", fmt.Errorf("failed to upload file to storage: %v", err)
	}
	defer resp.Body.Close()

	// Read storage response
	storageBodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("failed to read storage response: %v", err)
	}

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		log.Printf("Storage upload failed - Status: %d, Response: %s", resp.StatusCode, string(storageBodyBytes))
		return "", "", fmt.Errorf("storage upload failed with status: %d", resp.StatusCode)
	}

	// Parse storage response to get file ID
	var storageResponse map[string]interface{}
	if err := json.Unmarshal(storageBodyBytes, &storageResponse); err != nil {
		return "", "", fmt.Errorf("failed to parse storage response: %v", err)
	}

	// Extract file ID
	fileID, exists := storageResponse["$id"]
	if !exists {
		return "", "", fmt.Errorf("file ID not found in storage response")
	}

	fileIDStr, ok := fileID.(string)
	if !ok {
		return "", "", fmt.Errorf("file ID is not a string")
	}

	// Build the image URL for viewing
	imageURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s/view?project=%s&mode=admin",
		appwriteEndpoint, bucketID, fileIDStr, appwriteProjectID)

	return fileIDStr, imageURL, nil
}

// updateProfileImageURL finds the profile by UserID and updates the ProfileImage field
func updateProfileImageURL(userID, imageURL, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) error {
	// Step 1: Find the profile document by UserID
	documentID, err := findProfileDocumentIDByUserID(userID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection)
	if err != nil {
		return fmt.Errorf("failed to find profile: %v", err)
	}

	if documentID == "" {
		return fmt.Errorf("profile not found for UserID: %s", userID)
	}

	// Step 2: Update the profile document with the image URL
	updateURL := fmt.Sprintf("%s/databases/%s/collections/%s/documents/%s",
		appwriteEndpoint, appwriteDatabaseID, profileCollection, documentID)

	// Create the update payload
	updateData := map[string]interface{}{
		"data": map[string]interface{}{
			"ProfileImage": imageURL,
		},
	}

	// Convert to JSON
	jsonData, err := json.Marshal(updateData)
	if err != nil {
		return fmt.Errorf("failed to marshal update data: %v", err)
	}

	log.Printf("Updating profile document ID: %s with image URL", documentID)

	// Create HTTP request
	req, err := http.NewRequest("PATCH", updateURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create update request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to update profile: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read update response: %v", err)
	}

	log.Printf("Profile update response status: %d", resp.StatusCode)

	if resp.StatusCode != http.StatusOK {
		log.Printf("Profile update failed - Response: %s", string(body))
		return fmt.Errorf("profile update failed with status: %d", resp.StatusCode)
	}

	log.Printf("Profile image URL updated successfully for UserID: %s", userID)
	return nil
}

// findProfileDocumentIDByUserID searches for a profile by UserID and returns the document ID
func findProfileDocumentIDByUserID(userID, appwriteEndpoint, appwriteProjectID, appwriteKey, appwriteDatabaseID, profileCollection string) (string, error) {
	// Build the URL to search Profile collection
	url := fmt.Sprintf("%s/databases/%s/collections/%s/documents",
		appwriteEndpoint, appwriteDatabaseID, profileCollection)

	log.Printf("Searching for profile document with UserID: %s", userID)

	// Create HTTP request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create search request: %v", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to search profiles: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read search response: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("profile search failed with status: %d", resp.StatusCode)
	}

	// Parse response
	var searchResponse map[string]interface{}
	if err := json.Unmarshal(body, &searchResponse); err != nil {
		return "", fmt.Errorf("failed to parse search response: %v", err)
	}

	// Look for documents with matching UserID
	if documents, exists := searchResponse["documents"]; exists {
		if documentsArray, ok := documents.([]interface{}); ok {
			for _, doc := range documentsArray {
				if docMap, ok := doc.(map[string]interface{}); ok {
					if userIDField, exists := docMap["UserID"]; exists {
						if userIDStr, ok := userIDField.(string); ok && userIDStr == userID {
							if docID, exists := docMap["$id"]; exists {
								if docIDStr, ok := docID.(string); ok {
									log.Printf("Found profile document ID: %s for UserID: %s", docIDStr, userID)
									return docIDStr, nil
								}
							}
						}
					}
				}
			}
		}
	}

	log.Printf("No profile document found for UserID: %s", userID)
	return "", nil
}

// deleteImageFromStorage deletes an image file from Appwrite Storage (used for rollback)
func deleteImageFromStorage(fileID, appwriteEndpoint, appwriteProjectID, appwriteKey, bucketID string) {
	deleteURL := fmt.Sprintf("%s/storage/buckets/%s/files/%s", appwriteEndpoint, bucketID, fileID)

	req, err := http.NewRequest("DELETE", deleteURL, nil)
	if err != nil {
		log.Printf("Failed to create delete request for rollback: %v", err)
		return
	}

	req.Header.Set("X-Appwrite-Project", appwriteProjectID)
	req.Header.Set("X-Appwrite-Key", appwriteKey)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Printf("Failed to delete image from storage during rollback: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusNoContent {
		log.Printf("Successfully deleted image %s from storage during rollback", fileID)
	} else {
		log.Printf("Failed to delete image %s from storage during rollback - Status: %d", fileID, resp.StatusCode)
	}
}
